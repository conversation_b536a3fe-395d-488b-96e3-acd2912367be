# HXBK影像件服务去除加密功能修改记录

## 修改概述

根据业务需求，HXBK影像件上传服务现在只需要不加密的版本，因此对 `HXBKImageFileService.java` 进行了修改，去除了所有加密相关的功能。

## 修改时间

2025年7月11日

## 修改文件

- `capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/service/HXBKImageFileService.java`

## 具体修改内容

### 1. 移除导入

- 移除了 `import com.jinghang.capital.core.banks.hxbk.util.crypto.AESUtils;` 导入

### 2. 删除加密相关方法

- 删除了 `generateAesKeySeed(String serialNo)` 方法
- 删除了 `encryptFileData(byte[] fileData, String aesKeySeed)` 方法

### 3. 修改身份证影像件上传逻辑

在 `uploadIdCardImages()` 方法中：
- 移除了加密文件名生成逻辑（`encryptedFileName`）
- 移除了AES密钥种子生成调用
- 移除了文件加密处理逻辑
- 移除了加密文件数据存储
- 移除了加密文件索引行生成

**修改前：**
```java
String encryptedFileName = "en_" + fileName;
String aesKeySeed = generateAesKeySeed(applySerialNo);
byte[] encryptedData = encryptFileData(fileData, aesKeySeed);
fileDataMap.put(encryptedFileName, encryptedData);
String encFileTypeCode = "EN_" + fileTypeCode;
indexLines.add(String.format("TIANSHU,PLATFORM1,%s,%s,%s", applySerialNo, encFileTypeCode, encryptedFileName));
```

**修改后：**
只保留原文件处理逻辑，不再生成加密文件。

### 4. 修改人脸影像件上传逻辑

在 `uploadFaceImages()` 方法中：
- 移除了加密文件名生成逻辑（`encryptedFileName`）
- 移除了AES密钥种子生成调用
- 移除了文件加密处理逻辑
- 移除了加密文件数据存储
- 移除了加密文件索引行生成

**修改前：**
```java
String encryptedFileName = "en_" + fileName;
String aesKeySeed = generateAesKeySeed(applySerialNo);
byte[] encryptedData = encryptFileData(fileData, aesKeySeed);
fileDataMap.put(encryptedFileName, encryptedData);
indexLines.add(String.format("TIANSHU,PLATFORM1,%s,EN_PHOTO,%s", applySerialNo, encryptedFileName));
```

**修改后：**
只保留原文件处理逻辑，不再生成加密文件。

## 影响分析

### 正面影响

1. **简化业务逻辑**：去除了复杂的加密处理流程，代码更加简洁易维护
2. **提升性能**：不再需要进行文件加密操作，减少了CPU和IO开销
3. **减少存储空间**：不再生成加密文件，减少了一半的存储空间需求
4. **降低复杂度**：减少了临时文件创建和清理的复杂度

### 需要注意的事项

1. **数据安全**：确保业务方已经确认不需要加密传输，或者在其他层面已经有安全保障
2. **兼容性**：确保接收方系统能够正确处理不加密的文件格式
3. **索引文件**：索引文件中不再包含加密文件的记录，只包含原文件记录

## 测试建议

1. **功能测试**：验证身份证和人脸影像件能够正常上传到SFTP
2. **文件完整性测试**：确保上传的文件内容完整且可正常读取
3. **索引文件测试**：验证生成的索引文件格式正确，只包含原文件记录
4. **性能测试**：对比修改前后的上传性能，应该有所提升

## 相关依赖

修改后不再依赖：
- `com.jinghang.capital.core.banks.hxbk.util.crypto.AESUtils` 类

## 备注

此次修改完全移除了加密功能，如果将来需要重新启用加密，需要重新实现相关逻辑。建议在版本控制系统中保留修改前的版本作为参考。
